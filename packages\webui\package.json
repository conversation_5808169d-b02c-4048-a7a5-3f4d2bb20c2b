{"name": "@yesimbot/koishi-plugin-webui", "description": "", "version": "1.0.0", "main": "lib/index.js", "typings": "lib/index.d.ts", "files": ["lib", "dist", "src"], "author": "", "license": "", "scripts": {"lint": "eslint src --ext .ts"}, "repository": {"type": "git", "url": "git+https://github.com/koishijs/webui.git", "directory": "plugins/sandbox"}, "bugs": {"url": "https://github.com/koishijs/webui/issues"}, "homepage": "https://koishi.chat/plugins/console/sandbox.html", "keywords": ["bot", "chatbot", "koishi", "plugin"], "koishi": {"browser": true, "public": ["dist"], "description": {"en": "", "zh": ""}, "service": {"required": ["console", "yesimbot"]}}, "dependencies": {"@koishijs/console": "^5.30.9"}, "devDependencies": {"@koishijs/client": "^5.30.9", "@koishijs/plugin-console": "^5.30.9", "@koishijs/plugin-server": "^3.2.4", "@satorijs/components-vue": "^0.7.8", "koishi": "^4.18.8", "koishi-plugin-yesimbot": "^3.0.0-rc.1"}, "peerDependencies": {"@koishijs/plugin-console": "^5.30.9", "koishi": "^4.18.8", "koishi-plugin-yesimbot": "^3.0.0-rc.1"}}