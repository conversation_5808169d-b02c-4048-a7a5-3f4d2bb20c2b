{"extends": "../../tsconfig.base", "compilerOptions": {"rootDir": "src", "outDir": "lib", "target": "es2022", "module": "esnext", "declaration": true, "emitDeclarationOnly": true, "composite": true, "incremental": true, "skipLibCheck": true, "esModuleInterop": true, "moduleResolution": "bundler", "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node", "yml-register/types"]}, "include": ["src"]}