<template>
  <k-layout>
    <div class="config-manager">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <div class="title-section">
            <h1>YesImBot 配置管理</h1>
            <p class="subtitle">管理和配置您的 AI 智能体设置</p>
          </div>
          
          <div class="header-actions">
            <el-button-group>
              <el-button 
                v-for="view in viewModes" 
                :key="view.key"
                :type="currentView === view.key ? 'primary' : 'default'"
                @click="currentView = view.key"
              >
                <i :class="view.icon"></i>
                {{ view.name }}
              </el-button>
            </el-button-group>
          </div>
        </div>
        
        <!-- 状态指示器 -->
        <div class="status-indicators">
          <div class="status-item" :class="{ 'status-success': configValid, 'status-error': !configValid }">
            <i :class="configValid ? 'icon-check-circle' : 'icon-alert-circle'"></i>
            <span>{{ configValid ? '配置有效' : '配置存在问题' }}</span>
          </div>
          
          <div class="status-item">
            <i class="icon-clock"></i>
            <span>最后保存: {{ lastSaved || '未保存' }}</span>
          </div>
          
          <div class="status-item">
            <i class="icon-database"></i>
            <span>配置版本: {{ configVersion }}</span>
          </div>
        </div>
      </div>
      
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 导入导出视图 -->
        <div v-if="currentView === 'import-export'" class="view-container">
          <ConfigImportExport @config-imported="handleConfigImported" />
        </div>
        
        <!-- JSON 编辑器视图 -->
        <div v-if="currentView === 'json-editor'" class="view-container">
          <JsonEditor 
            :initial-config="currentConfig"
            @config-changed="handleConfigChanged"
            @config-saved="handleConfigSaved"
          />
        </div>
        
        <!-- 表单编辑器视图 -->
        <div v-if="currentView === 'form-editor'" class="view-container">
          <ConfigForm 
            :config="currentConfig"
            @config-changed="handleConfigChanged"
            @config-saved="handleConfigSaved"
          />
        </div>
        
        <!-- 配置预览视图 -->
        <div v-if="currentView === 'preview'" class="view-container">
          <k-card>
            <template #header>
              <div class="preview-header">
                <h3>配置预览</h3>
                <div class="preview-actions">
                  <el-button size="small" @click="refreshPreview">
                    <i class="icon-refresh"></i>
                    刷新
                  </el-button>
                  <el-button size="small" @click="exportPreview">
                    <i class="icon-download"></i>
                    导出
                  </el-button>
                </div>
              </div>
            </template>
            
            <div class="config-preview">
              <!-- 配置概览 -->
              <div class="preview-summary">
                <div class="summary-grid">
                  <div class="summary-item">
                    <div class="summary-label">模型提供商</div>
                    <div class="summary-value">{{ getProviderCount() }}</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">配置的模型</div>
                    <div class="summary-value">{{ getModelCount() }}</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">模型组</div>
                    <div class="summary-value">{{ getModelGroupCount() }}</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">允许频道</div>
                    <div class="summary-value">{{ getChannelCount() }}</div>
                  </div>
                </div>
              </div>
              
              <!-- 配置详情 -->
              <div class="preview-details">
                <el-collapse v-model="activePreviewSections">
                  <el-collapse-item 
                    v-for="section in previewSections" 
                    :key="section.key"
                    :title="section.title"
                    :name="section.key"
                  >
                    <div class="section-content">
                      <pre class="config-json">{{ formatSectionConfig(section.key) }}</pre>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </k-card>
        </div>
      </div>
      
      <!-- 底部操作栏 -->
      <div class="bottom-actions">
        <div class="actions-left">
          <el-button @click="resetAllConfig" :disabled="!hasChanges">
            <i class="icon-refresh-cw"></i>
            重置所有配置
          </el-button>
          
          <el-button @click="validateAllConfig">
            <i class="icon-check-square"></i>
            验证配置
          </el-button>
        </div>
        
        <div class="actions-right">
          <el-button @click="discardChanges" :disabled="!hasChanges">
            取消更改
          </el-button>
          
          <el-button type="primary" @click="saveAllConfig" :disabled="!hasChanges || !configValid">
            <i class="icon-save"></i>
            保存配置
          </el-button>
        </div>
      </div>
      
      <!-- 帮助面板 -->
      <div v-if="showHelp" class="help-panel">
        <k-card>
          <template #header>
            <div class="help-header">
              <h4>配置帮助</h4>
              <el-button size="small" @click="showHelp = false">
                <i class="icon-x"></i>
              </el-button>
            </div>
          </template>
          
          <div class="help-content">
            <div class="help-section">
              <h5>快速开始</h5>
              <ul>
                <li>使用"导入导出"功能快速导入现有配置文件</li>
                <li>使用"表单编辑器"进行结构化配置编辑</li>
                <li>使用"JSON编辑器"进行高级配置修改</li>
                <li>使用"配置预览"查看当前配置概览</li>
              </ul>
            </div>
            
            <div class="help-section">
              <h5>注意事项</h5>
              <ul>
                <li>修改配置前建议先导出备份</li>
                <li>API密钥等敏感信息请妥善保管</li>
                <li>配置修改后需要重启服务才能生效</li>
              </ul>
            </div>
          </div>
        </k-card>
      </div>
      
      <!-- 浮动帮助按钮 -->
      <el-button 
        class="help-button"
        type="primary"
        circle
        @click="showHelp = !showHelp"
      >
        <i class="icon-help-circle"></i>
      </el-button>
    </div>
  </k-layout>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ConfigImportExport from '../components/ConfigImportExport.vue'
import JsonEditor from '../components/JsonEditor.vue'
import ConfigForm from '../components/ConfigForm.vue'

// 视图模式
const viewModes = [
  { key: 'import-export', name: '导入导出', icon: 'icon-upload-cloud' },
  { key: 'form-editor', name: '表单编辑', icon: 'icon-edit-3' },
  { key: 'json-editor', name: 'JSON编辑', icon: 'icon-code' },
  { key: 'preview', name: '配置预览', icon: 'icon-eye' }
]

// 预览部分
const previewSections = [
  { key: 'modelService', title: '模型服务配置' },
  { key: 'agentBehavior', title: '智能体行为配置' },
  { key: 'capabilities', title: '能力配置' },
  { key: 'assetService', title: '资源服务配置' },
  { key: 'promptService', title: '提示词服务配置' },
  { key: 'system', title: '系统配置' }
]

// 响应式数据
const currentView = ref('form-editor')
const showHelp = ref(false)
const configValid = ref(true)
const hasChanges = ref(false)
const lastSaved = ref<string>('')
const configVersion = ref('1.0.0')
const activePreviewSections = ref(['modelService', 'agentBehavior'])

// 当前配置数据
const currentConfig = ref({
  modelService: {
    providers: [],
    modelGroups: [],
    task: { chat: '', embedding: '' }
  },
  agentBehavior: {
    arousal: {
      allowedChannels: [{ platform: 'onebot', type: 'guild', id: '*' }],
      debounceMs: 1000
    },
    willingness: {
      base: { text: 10 },
      attribute: { atMention: 100, isQuote: 15, isDirectMessage: 40 },
      lifecycle: { maxWillingness: 100, decayHalfLifeSeconds: 90 }
    },
    streamAction: false,
    heartbeat: 5
  },
  capabilities: {
    memory: { coreMemoryPath: 'data/yesimbot/memory/core' },
    history: {
      l1_memory: { maxMessages: 50 },
      l2_memory: { enabled: true, retrievalK: 8, retrievalMinSimilarity: 0.55 }
    },
    tools: { advanced: { maxRetry: 3, retryDelay: 1000, timeout: 10000 } }
  },
  assetService: {
    storagePath: 'data/assets',
    driver: 'local',
    maxFileSize: 100
  },
  promptService: {
    injectionPlaceholder: 'extensions',
    maxRenderDepth: 3
  },
  system: {
    logging: {},
    errorReporting: {}
  }
})

// 计算属性
const getProviderCount = () => {
  return currentConfig.value.modelService.providers?.length || 0
}

const getModelCount = () => {
  return currentConfig.value.modelService.providers?.reduce((count, provider) => {
    return count + (provider.models?.length || 0)
  }, 0) || 0
}

const getModelGroupCount = () => {
  return currentConfig.value.modelService.modelGroups?.length || 0
}

const getChannelCount = () => {
  return currentConfig.value.agentBehavior.arousal.allowedChannels?.length || 0
}

// 方法
const handleConfigImported = (config: any) => {
  currentConfig.value = { ...currentConfig.value, ...config }
  hasChanges.value = true
  ElMessage.success('配置已导入')
}

const handleConfigChanged = (config: any) => {
  currentConfig.value = config
  hasChanges.value = true
}

const handleConfigSaved = () => {
  hasChanges.value = false
  lastSaved.value = new Date().toLocaleString()
  ElMessage.success('配置已保存')
}

const refreshPreview = () => {
  ElMessage.info('预览已刷新')
}

const exportPreview = () => {
  const blob = new Blob([JSON.stringify(currentConfig.value, null, 2)], { 
    type: 'application/json' 
  })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `yesimbot-config-${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  ElMessage.success('配置已导出')
}

const formatSectionConfig = (sectionKey: string) => {
  const sectionConfig = currentConfig.value[sectionKey as keyof typeof currentConfig.value]
  return JSON.stringify(sectionConfig, null, 2)
}

const resetAllConfig = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有配置吗？此操作不可撤销。', '确认重置', {
      type: 'warning'
    })
    
    // 重置配置逻辑
    hasChanges.value = false
    ElMessage.success('配置已重置')
  } catch {
    // 用户取消
  }
}

const validateAllConfig = () => {
  // 配置验证逻辑
  configValid.value = true
  ElMessage.success('配置验证通过')
}

const discardChanges = async () => {
  try {
    await ElMessageBox.confirm('确定要放弃所有未保存的更改吗？', '确认放弃', {
      type: 'warning'
    })
    
    // 恢复配置逻辑
    hasChanges.value = false
    ElMessage.info('已放弃更改')
  } catch {
    // 用户取消
  }
}

const saveAllConfig = async () => {
  try {
    // 保存配置到后端的逻辑
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟API调用
    
    handleConfigSaved()
  } catch (error) {
    ElMessage.error('保存配置失败')
  }
}

// 初始化
onMounted(() => {
  // 加载当前配置
  lastSaved.value = new Date().toLocaleString()
})
</script>

<style scoped>
.config-manager {
  min-height: 100vh;
  background: var(--bg-color);
}

.page-header {
  background: var(--bg-color-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 2rem;
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.title-section h1 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
}

.subtitle {
  margin: 0;
  color: var(--text-color-secondary);
}

.status-indicators {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.status-success {
  color: var(--success-color);
}

.status-error {
  color: var(--error-color);
}

.main-content {
  padding: 0 2rem;
  margin-bottom: 2rem;
}

.view-container {
  min-height: 600px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
}

.preview-summary {
  margin-bottom: 2rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  background: var(--bg-color-secondary);
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.summary-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  margin-bottom: 0.5rem;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.config-json {
  background: var(--bg-color-tertiary);
  padding: 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  max-height: 300px;
  overflow-y: auto;
}

.bottom-actions {
  position: sticky;
  bottom: 0;
  background: var(--bg-color-secondary);
  border-top: 1px solid var(--border-color);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.actions-left, .actions-right {
  display: flex;
  gap: 0.5rem;
}

.help-panel {
  position: fixed;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  width: 300px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.help-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.help-section {
  margin-bottom: 1rem;
}

.help-section h5 {
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.help-section ul {
  margin: 0;
  padding-left: 1.5rem;
}

.help-section li {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.help-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 999;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .main-content {
    padding: 0 1rem;
  }
  
  .bottom-actions {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .actions-left, .actions-right {
    width: 100%;
    justify-content: center;
  }
  
  .help-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    transform: none;
    margin: 1rem;
  }
}
</style>
