name: "Canary Release"

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'The npm dist-tag for this canary release (e.g., "canary", "test")'
        required: true
        default: "canary"
      reason:
        description: "Reason for this manual release"
        required: false

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  publish-canary:
    name: Publish Canary Version
    runs-on: ubuntu-latest

    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup B<PERSON>
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Install Dependencies
        run: bun install --frozen-lockfile

      - name: Build All Packages
        run: bun turbo run build

      - name: Creating .npmrc
        run: |
          cat << EOF > "$HOME/.npmrc"
            //registry.npmjs.org/:_authToken=${NPM_TOKEN}
          EOF
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: Create Canary Version
        run: bun changeset version --snapshot ${{ github.event.inputs.tag }}

      # - name: Optimize Canary Version Format
      #   run: bun run optimize-canary-version

      - name: Publish to NPM
        run: bun changeset publish --tag ${{ github.event.inputs.tag }}
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
