name: Bug Report
description: 提交错误报告
title: "Bug: "
labels:
  - bug
body:
  - type: textarea
    attributes:
      label: Describe the bug
      description: 请简明地表述 bug 是什么。
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps to reproduce
      description: 请描述如何重现这个行为。
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected behavior
      description: 请描述期望的行为。
    validations:
      required: true
  - type: textarea
    attributes:
      label: Screenshots
      description: 请尽量详细地提供相关截图，可以是聊天记录、Koishi 日志和服务端（如 go-cqhttp）日志等。
  - type: textarea
    attributes:
      label: Versions
      description: 请填写相应的版本号。
      value: |
        - OS: <!-- e.g. Windows 10 -->
        - Platform: <!-- e.g. Discord -->
        - Node version: <!-- e.g. 14.17.0 -->
        - Koishi version: <!-- e.g. 3.0.0 -->
        - YesImBot version: <!-- e.g. 1.0.0 -->
    validations:
      required: true
  - type: textarea
    attributes:
      label: Additional context
      description: 请描述其他想要补充的信息。
