{"name": "@yesimbot/koishi-plugin-code-executor", "description": "Yes! I'm <PERSON><PERSON>! 代码执行器扩展插件", "version": "1.0.0", "main": "lib/index.js", "typings": "lib/index.d.ts", "homepage": "https://github.com/YesWeAreBot/YesImBot", "files": ["lib", "dist", "resources"], "scripts": {"build": "tsc && node esbuild.config.mjs", "dev": "tsc -w --preserveWatchOutput", "lint": "eslint . --ext .ts", "clean": "rm -rf lib .turbo tsconfig.tsbuildinfo *.tgz", "pack": "bun pm pack"}, "license": "MIT", "contributors": ["MiaowFISH <<EMAIL>>"], "keywords": ["koishi", "plugin", "code", "interpreter", "yesimbot", "extension"], "repository": {"type": "git", "url": "git+https://github.com/YesWeAreBot/YesImBot.git", "directory": "packages/code-executor"}, "bugs": {"url": "https://github.com/YesWeAreBot/YesImBot/issues"}, "dependencies": {"isolated-vm": "5.0.4", "pyodide": "0.28.2"}, "devDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "^3.0.0-rc.1"}, "peerDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "^3.0.0-rc.1"}, "publishConfig": {"access": "public"}, "koishi": {"description": {"zh": "为 YesImBot 提供一个安全、隔离的 JavaScript 代码执行器", "en": "Provides a secure and isolated JavaScript code interpreter for the YesImBot"}, "service": {"required": ["yesimbot"]}}}