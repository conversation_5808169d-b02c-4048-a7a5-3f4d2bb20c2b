{{#is_message}}
<message>[{{id}}|{{#timestamp}}{{_formatDate}}{{/timestamp}}|{{sender.name}}({{sender.id}})] {{content}}</message>
{{/is_message}}
{{#is_agent_thought}}
<thoughts>
  <observe>{{observe}}</observe>
  <analyze_infer>{{analyze_infer}}</analyze_infer>
  <plan>{{plan}}</plan>
</thoughts>
{{/is_agent_thought}}
{{#is_agent_action}}
<action>
  <function>{{function}}</function>
  <params>{{_renderParams}}</params>
</action>
{{/is_agent_action}}
{{#is_agent_observation}}
<observation>
  <function>{{function}}</function>
  <status>{{status}}</status>
  {{#result}}
  <result>{{#.}}{{_toString}}{{/.}}</result>
  {{/result}}
  {{#error}}
  <error>{{.}}</error>
  {{/error}}
</observation>
{{/is_agent_observation}}
{{#is_agent_heartbeat}}
<heartbeat current={{current}} max={{max}}/>
{{/is_agent_heartbeat}}
{{#is_system_event}}
<system_event>[{{#timestamp}}{{_formatDate}}{{/timestamp}}|System] {{message}}</system_event>
{{/is_system_event}}