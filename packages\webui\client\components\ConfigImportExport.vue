<template>
  <k-card>
    <template #header>
      <h3>配置文件导入导出</h3>
    </template>
    
    <div class="import-export-container">
      <!-- 导入区域 -->
      <div class="import-section">
        <h4>导入配置</h4>
        <div class="upload-area" @click="triggerFileInput" @dragover.prevent @drop="handleFileDrop">
          <input 
            ref="fileInput" 
            type="file" 
            accept=".json,.yml,.yaml" 
            @change="handleFileSelect" 
            style="display: none"
          />
          <div class="upload-content">
            <i class="icon-upload"></i>
            <p>点击选择文件或拖拽文件到此处</p>
            <p class="file-types">支持 JSON、YAML 格式</p>
          </div>
        </div>
        
        <!-- 文件预览 -->
        <div v-if="selectedFile" class="file-preview">
          <div class="file-info">
            <span class="file-name">{{ selectedFile.name }}</span>
            <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
          </div>
          <div class="preview-content">
            <pre v-if="previewContent">{{ previewContent }}</pre>
            <div v-else class="loading">正在解析文件...</div>
          </div>
          <div class="import-actions">
            <el-button @click="clearFile">清除</el-button>
            <el-button type="primary" @click="importConfig" :disabled="!previewContent">
              导入配置
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 导出区域 -->
      <div class="export-section">
        <h4>导出配置</h4>
        <div class="export-options">
          <div class="format-selection">
            <label>导出格式：</label>
            <el-radio-group v-model="exportFormat">
              <el-radio label="json">JSON</el-radio>
              <el-radio label="yaml">YAML</el-radio>
            </el-radio-group>
          </div>
          
          <div class="export-scope">
            <label>导出范围：</label>
            <el-checkbox-group v-model="exportSections">
              <el-checkbox label="modelService">模型服务</el-checkbox>
              <el-checkbox label="agentBehavior">智能体行为</el-checkbox>
              <el-checkbox label="capabilities">能力配置</el-checkbox>
              <el-checkbox label="assetService">资源服务</el-checkbox>
              <el-checkbox label="promptService">提示词服务</el-checkbox>
              <el-checkbox label="system">系统配置</el-checkbox>
            </el-checkbox-group>
          </div>
          
          <div class="export-actions">
            <el-button @click="previewExport">预览导出</el-button>
            <el-button type="primary" @click="exportConfig">
              导出配置文件
            </el-button>
          </div>
        </div>
        
        <!-- 导出预览 -->
        <div v-if="exportPreview" class="export-preview">
          <h5>导出预览</h5>
          <pre class="preview-code">{{ exportPreview }}</pre>
        </div>
      </div>
    </div>
  </k-card>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 响应式数据
const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)
const previewContent = ref<string>('')
const exportFormat = ref<'json' | 'yaml'>('json')
const exportSections = ref<string[]>(['modelService', 'agentBehavior', 'capabilities'])
const exportPreview = ref<string>('')

// 模拟的当前配置数据（实际应该从后端获取）
const currentConfig = ref({
  modelService: {
    providers: [],
    modelGroups: [],
    task: { chat: '', embedding: '' }
  },
  agentBehavior: {
    arousal: { allowedChannels: [], debounceMs: 1000 },
    willingness: {},
    streamAction: false,
    heartbeat: 5
  },
  capabilities: {
    memory: { coreMemoryPath: 'data/yesimbot/memory/core' },
    history: {},
    tools: {}
  },
  assetService: {
    storagePath: 'data/assets',
    driver: 'local'
  },
  promptService: {
    injectionPlaceholder: 'extensions',
    maxRenderDepth: 3
  },
  system: {
    logging: {},
    errorReporting: {}
  }
})

// 方法
const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    processFile(target.files[0])
  }
}

const handleFileDrop = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer?.files && event.dataTransfer.files[0]) {
    processFile(event.dataTransfer.files[0])
  }
}

const processFile = async (file: File) => {
  selectedFile.value = file
  previewContent.value = ''
  
  try {
    const text = await file.text()
    if (file.name.endsWith('.json')) {
      // 验证JSON格式
      JSON.parse(text)
      previewContent.value = JSON.stringify(JSON.parse(text), null, 2)
    } else if (file.name.endsWith('.yml') || file.name.endsWith('.yaml')) {
      // 这里应该使用YAML解析库，暂时直接显示
      previewContent.value = text
    }
  } catch (error) {
    ElMessage.error('文件格式错误，请检查文件内容')
    console.error('File parsing error:', error)
  }
}

const clearFile = () => {
  selectedFile.value = null
  previewContent.value = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const importConfig = () => {
  if (!previewContent.value) return
  
  try {
    const config = JSON.parse(previewContent.value)
    // 这里应该调用后端API来应用配置
    ElMessage.success('配置导入成功！')
    clearFile()
  } catch (error) {
    ElMessage.error('配置导入失败，请检查文件格式')
    console.error('Import error:', error)
  }
}

const previewExport = () => {
  const exportData: any = {}
  
  exportSections.value.forEach(section => {
    if (currentConfig.value[section as keyof typeof currentConfig.value]) {
      exportData[section] = currentConfig.value[section as keyof typeof currentConfig.value]
    }
  })
  
  if (exportFormat.value === 'json') {
    exportPreview.value = JSON.stringify(exportData, null, 2)
  } else {
    // 这里应该使用YAML库转换，暂时用JSON格式
    exportPreview.value = JSON.stringify(exportData, null, 2)
  }
}

const exportConfig = () => {
  previewExport()
  
  if (!exportPreview.value) {
    ElMessage.warning('没有可导出的配置')
    return
  }
  
  const blob = new Blob([exportPreview.value], { 
    type: exportFormat.value === 'json' ? 'application/json' : 'text/yaml' 
  })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `yesimbot-config.${exportFormat.value}`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  ElMessage.success('配置文件已导出')
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.import-export-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

.import-section, .export-section {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
}

.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
  margin: 1rem 0;
}

.upload-area:hover {
  border-color: var(--primary-color);
}

.upload-content i {
  font-size: 2rem;
  color: var(--text-color-secondary);
  margin-bottom: 1rem;
}

.file-types {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  margin-top: 0.5rem;
}

.file-preview {
  margin-top: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 1rem;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.file-name {
  font-weight: 500;
}

.file-size {
  color: var(--text-color-secondary);
  font-size: 0.875rem;
}

.preview-content {
  max-height: 200px;
  overflow-y: auto;
  background: var(--bg-color-secondary);
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.preview-content pre {
  margin: 0;
  font-size: 0.875rem;
  white-space: pre-wrap;
}

.import-actions, .export-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.export-options > div {
  margin-bottom: 1rem;
}

.export-options label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.export-preview {
  margin-top: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 1rem;
}

.preview-code {
  max-height: 300px;
  overflow-y: auto;
  background: var(--bg-color-secondary);
  border-radius: 4px;
  padding: 1rem;
  margin: 0;
  font-size: 0.875rem;
  white-space: pre-wrap;
}

.loading {
  text-align: center;
  color: var(--text-color-secondary);
  padding: 2rem;
}

@media (max-width: 768px) {
  .import-export-container {
    grid-template-columns: 1fr;
  }
}
</style>
