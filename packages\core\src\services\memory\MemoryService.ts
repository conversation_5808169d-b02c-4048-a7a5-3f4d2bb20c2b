import fs from "fs/promises";
import { Context, Service } from "koishi";
import path from "path";

import { RESOURCES_DIR, Services } from "@/shared/constants";
import { MemoryConfig } from "./config";
import { MemoryBlock, MemoryBlockData } from "./MemoryBlock";

declare module "koishi" {
    interface Context {
        [Services.Memory]: MemoryService;
    }
}

export class MemoryService extends Service<MemoryConfig> {
    static readonly inject = [Services.Logger];

    private coreMemoryBlocks: Map<string, MemoryBlock> = new Map();

    constructor(ctx: Context, config: MemoryConfig) {
        super(ctx, Services.Memory, true);
        this.config = config;
        this.logger = ctx[Services.Logger].getLogger("[核心记忆]");
    }

    protected start() {
        this.loadCoreMemoryBlocks();
    }

    public getMemoryBlocksForRendering(): MemoryBlockData[] {
        return Array.from(this.coreMemoryBlocks.values()).map((block) => block.toData());
    }

    /**
     * 扫描核心记忆目录，加载所有可用的记忆块
     */
    public async loadCoreMemoryBlocks() {
        const memoryPath = this.config.coreMemoryPath;
        try {
            await fs.mkdir(memoryPath, { recursive: true });
            const files = await fs.readdir(memoryPath);
            const memoryFiles = files.filter((file) => file.endsWith(".md") || file.endsWith(".txt"));

            if (memoryFiles.length === 0) {
                this.logger.warn(`核心记忆目录 '${memoryPath}' 为空，将应用默认设定`);
                try {
                    const defaultMemoryFiles = await fs.readdir(path.join(RESOURCES_DIR, "memory_block"));

                    for (const file of defaultMemoryFiles) {
                        await fs.copyFile(path.join(RESOURCES_DIR, "memory_block", file), path.join(memoryPath, file));
                    }

                    this.loadCoreMemoryBlocks();
                } catch (error) {
                    this.logger.error(`复制默认记忆块失败: ${error.message}`);
                }
                return;
            }

            for (const file of memoryFiles) {
                const filePath = path.join(memoryPath, file);
                try {
                    const block = await MemoryBlock.createFromFile(this.ctx, filePath);
                    if (this.coreMemoryBlocks.has(block.label)) {
                        this.logger.warn(`发现重复的记忆块标签 '${block.label}'，来自文件 '${filePath}'已忽略`);
                    } else {
                        this.coreMemoryBlocks.set(block.label, block);
                        this.logger.debug(`已从文件 '${file}' 加载核心记忆块 '${block.label}'`);
                    }
                } catch (error) {
                    //this.logger.error(`加载记忆块文件 '${filePath}' 失败: ${error.message}`);
                }
            }
        } catch (error) {
            this.logger.error(`扫描核心记忆目录 '${memoryPath}' 失败: ${error.message}`);
        }
    }
}
