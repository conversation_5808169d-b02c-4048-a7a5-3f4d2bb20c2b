{"name": "koishi-plugin-yesimbot", "description": "Yes! I'm <PERSON><PERSON>! 机械壳，人类心", "version": "3.0.0-rc.1", "main": "lib/index.js", "typings": "lib/index.d.ts", "homepage": "https://github.com/YesWeAreBot/YesImBot", "files": ["lib", "dist", "resources"], "contributors": ["HydroGest <<EMAIL>>", "Dispure <<EMAIL>>", "Miaowfish <<EMAIL>>", "Touch-Night <<EMAIL>>"], "scripts": {"build": "tsc && tsc-alias && node scripts/bundle.mjs", "dev": "tsc -w --preserveWatchOutput", "lint": "eslint . --ext .ts", "clean": "rm -rf lib .turbo tsconfig.tsbuildinfo *.tgz", "pack": "bun pm pack"}, "license": "MIT", "keywords": ["chatbot", "koishi", "plugin", "ai"], "repository": {"type": "git", "url": "git+https://github.com/YesWeAreBot/YesImBot.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/YesWeAreBot/YesImBot/issues"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json", "./services": {"types": "./lib/services/index.d.ts", "import": "./lib/services/index.mjs", "require": "./lib/services/index.js"}, "./shared": {"types": "./lib/shared/index.d.ts", "import": "./lib/shared/index.mjs", "require": "./lib/shared/index.js"}}, "dependencies": {"@miaowfish/gifwrap": "^0.10.1", "gray-matter": "^4.0.3", "jimp": "^1.6.0", "jsonrepair": "^3.12.0", "mustache": "^4.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@xsai-ext/providers-cloud": "^0.3.2", "@xsai-ext/providers-local": "^0.3.2", "@xsai-ext/shared-providers": "^0.3.2", "@xsai/embed": "^0.3.2", "@xsai/generate-text": "^0.3.2", "@xsai/shared-chat": "^0.3.2", "@xsai/stream-text": "^0.3.2", "@xsai/utils-chat": "^0.3.2", "koishi": "^4.18.7", "koishi-plugin-adapter-onebot": "^6.8.0"}, "peerDependencies": {"koishi": "^4.18.7"}, "koishi": {"description": {"zh": "让语言大模型机器人假装群友并和群友聊天！", "en": "A Koishi plugin that allows LLM chat in your guild."}, "browser": true, "service": {"required": ["database"], "implements": ["yesimbot"]}}}