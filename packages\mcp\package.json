{"name": "@yesimbot/koishi-plugin-mcp", "description": "Yes! I'm <PERSON><PERSON>! MCP 扩展插件", "version": "1.0.0", "main": "lib/index.js", "typings": "lib/index.d.ts", "homepage": "https://github.com/HydroGest/YesImBot", "files": ["lib", "dist", "resources"], "scripts": {"build": "tsc && node esbuild.config.mjs", "dev": "tsc -w --preserveWatchOutput", "lint": "eslint . --ext .ts", "clean": "rm -rf lib .turbo tsconfig.tsbuildinfo *.tgz", "pack": "bun pm pack"}, "license": "MIT", "contributors": ["MiaowFISH <<EMAIL>>"], "keywords": ["koishi", "plugin", "mcp", "modelcontextprotocol", "yesimbot"], "repository": {"type": "git", "url": "git+https://github.com/HydroGest/YesImBot.git", "directory": "packages/mcp"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.0", "yauzl": "^3.2.0"}, "devDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "^3.0.0-rc.1"}, "peerDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "^3.0.0-rc.1"}, "koishi": {"description": {"zh": "Yes! I'm <PERSON><PERSON>! MCP 扩展插件", "en": "Yes! I'm Bot! MCP extension plugin"}, "service": {"required": ["yesimbot"], "implements": ["yesimbot-extension-mcp"]}}}