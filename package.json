{"name": "@root/yesimbot", "version": "0.0.0", "packageManager": "bun@1.2.0", "private": true, "homepage": "https://github.com/HydroGest/YesImBot", "contributors": ["HydroGest <<EMAIL>>", "Dispure <<EMAIL>>", "Miaowfish <<EMAIL>>", "Touch-Night <<EMAIL>>"], "license": "MIT", "workspaces": ["packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean", "pack": "turbo run pack", "prepare": "husky install", "add-changeset": "changeset add", "version-packages": "changeset version", "release": "bun run build && changeset publish", "create-packages": "changeset version && turbo run pack", "collect-packages": "bun scripts/collect-packages.js", "optimize-canary-version": "node scripts/optimize-canary-version.js", "sync-npmmirror": "node scripts/sync-npmmirror.js", "sync-npmmirror:test": "node scripts/sync-npmmirror.js --dry-run", "commit": "cz"}, "devDependencies": {"@changesets/cli": "^2.29.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/node": "^22.16.2", "commitizen": "^4.3.1", "cz-git": "^1.12.0", "esbuild": "^0.25.6", "glob": "^11.0.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "tsc-alias": "^1.8.16", "turbo": "2.5.4", "typescript": "^5.8.3", "yml-register": "^1.2.5"}, "lint-staged": {"*.{js,ts,jsx,tsx,json,md,yml}": "prettier --write"}, "config": {"commitizen": {"path": "cz-git"}}}