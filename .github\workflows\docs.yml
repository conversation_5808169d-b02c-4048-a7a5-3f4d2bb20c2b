name: Generate and Deploy TS API Docs

on:
    push:
        branches: [dev]

jobs:
    generate-and-deploy-docs:
        runs-on: ubuntu-latest
        timeout-minutes: 15
        permissions:
            contents: write
        steps:
            - name: Check out repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 1

            - name: Setup Bun
              uses: oven-sh/setup-bun@v2
              with:
                  bun-version: latest

            - name: Cache dependencies
              uses: actions/cache@v4
              with:
                  path: ~/.bun/install/cache
                  key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}
                  restore-keys: |
                      ${{ runner.os }}-bun-

            - name: Install dependencies
              run: bun install --frozen-lockfile

            - name: Install TypeDoc
              run: bun add -D typedoc typedoc-plugin-markdown typedoc-plugin-rename-defaults

            - name: Generate API documentation
              run: bunx typedoc
              env:
                  NODE_OPTIONS: --max-old-space-size=4096

            - name: Deploy to GitHub Pages
              uses: peaceiris/actions-gh-pages@v4
              with:
                  github_token: ${{ secrets.GITHUB_TOKEN }}
                  publish_branch: gh-pages
                  publish_dir: ./docs
                  user_name: "github-actions[bot]"
                  user_email: "github-actions[bot]@users.noreply.github.com"
                  commit_message: "docs: deploy generated API documentation [skip ci]"
