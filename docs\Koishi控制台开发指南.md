# Koishi 控制台开发指南

本指南旨在为开发者提供一个全面的 Koishi 控制台扩展开发参考。无论您是想添加一个新页面、与后端进行数据交互，还是自定义控制台主题，这里都能找到您需要的信息。

## 1. 快速开始

本章节将引导您完成环境搭建，并创建一个最基础的控制台扩展。

### 环境搭建

1.  **前置要求**:
    *   一个正常运行的 Koishi 项目。
    *   Node.js v16.15 或更高版本。

2.  **安装依赖**:
    您的插件需要依赖 `@koishijs/plugin-console` 来与控制台核心交互，并需要 `@koishijs/client` 作为开发依赖来获取前端类型和 API。

    在您的插件 `package.json` 中添加以下字段：

    ```json title="package.json"
    {
      "peerDependencies": {
        "@koishijs/plugin-console": "^5.11.0"
      },
      "devDependencies": {
        "@koishijs/client": "^5.11.0"
      }
    }
    ```

    然后运行 `npm install` 或您使用的包管理器来安装依赖。

### 项目结构

一个典型的控制台插件包含前端 (`client`) 和后端 (`src`) 两部分。

```diff
└── my-plugin
+   ├── client
+   │   ├── index.ts      # 前端入口文件
+   │   ├── page.vue      # 页面组件
+   │   └── tsconfig.json # 前端 TypeScript 配置
    ├── src
    │   └── index.ts      # 插件后端入口
    ├── package.json
    └── tsconfig.json
```

### 第一个页面

1.  **创建前端文件**

    **`client/page.vue`**: 这是一个标准的 Vue 单文件组件。`<k-layout>` 和 `<k-card>` 是 Koishi 提供的内置布局组件。

    ```vue title="client/page.vue"
    <template>
      <k-layout>
        <k-card>这是我的第一个控制台页面！</k-card>
      </k-layout>
    </template>
    ```

    **`client/index.ts`**: 这是前端插件的入口。它接收一个客户端的 `Context` 对象，您可以用它来注册页面、插槽等。

    ```ts title="client/index.ts"
    import { Context } from '@koishijs/client'
    import Page from './page.vue'

    export default (ctx: Context) => {
      // 注册一个新页面
      ctx.page({
        name: '我的页面',         // 页面标题
        path: '/my-plugin/page', // 页面路由
        component: Page,         // 页面使用的 Vue 组件
      })
    }
    ```

    **`client/tsconfig.json`**: 为前端代码提供 TypeScript 支持和类型提示。

    ```json title="client/tsconfig.json"
    {
      "compilerOptions": {
        "rootDir": ".",
        "module": "esnext",
        "moduleResolution": "node",
        "types": [
          "@koishijs/client/global"
        ]
      },
      "include": ["."]
    }
    ```

2.  **连接前后端**

    修改您的插件后端入口文件 `src/index.ts`，告诉 Koishi 控制台如何加载您的前端资源。

    ```ts title="src/index.ts"
    import { Context } from 'koishi'
    import {} from '@koishijs/plugin-console' // 导入类型
    import { resolve } from 'path'

    export const name = 'my-plugin'

    // 确保依赖 console 服务
    export const inject = ['console']

    export function apply(ctx: Context) {
      // 注册前端入口
      ctx.console.addEntry({
        // 开发环境下，Koishi 会使用 esbuild 直接加载 .ts 文件
        dev: resolve(__dirname, '../client/index.ts'),
        // 生产环境下，加载编译后的文件
        // 通常需要配合打包工具 (如 Vite) 使用
        prod: resolve(__dirname, '../dist'),
      })
    }
    ```

3.  **运行与调试**
    现在，在开发模式下启动您的 Koishi 实例。打开控制台，在侧边栏应该就能看到您刚刚创建的“我的页面”了。

## 2. 核心概念

理解以下核心概念将帮助您更好地进行控制台开发。

### 前后端分离架构

Koishi 控制台采用前后端分离的设计。
*   **后端 (`src`)**: 一个标准的 Koishi 插件，运行在 Node.js 环境，负责处理业务逻辑、数据持久化，并通过 WebSocket 与前端通信。
*   **前端 (`client`)**: 一个基于 Vue.js 的单页应用，运行在用户的浏览器中，负责界面展示和用户交互。
两者通过 `ctx.console.addEntry` 方法关联起来。

### 插件化的客户端

Koishi 控制台的前端本身也是一个插件化系统。`@koishijs/client` 库在浏览器环境中实现了一套与后端类似的 `Context` API。这使得前端功能也能像后端一样被模块化地组织和扩展。

### 数据交互模式

前后端数据交互主要通过 WebSocket 进行，Koishi 提供了两种主流模式：

1.  **被动推送 (DataService)**:
    *   **场景**: 用于前端需要持续同步的后端状态，例如机器人列表、统计数据等。
    *   **工作流**: 后端定义一个 `DataService`，它负责提供数据。前端在页面中声明依赖此数据 (`fields: ['service-key']`)。控制台会自动获取数据并注入到全局响应式 `store` 对象中。
    *   **优点**: 自动管理数据同步，数据是响应式的，后端可通过 `service.refresh()` 主动推送更新。

2.  **主动获取 (事件)**:
    *   **场景**: 用于执行一次性操作或获取非持续性数据，例如保存文件、执行某个指令。
    *   **工作流**: 后端使用 `ctx.console.addListener('event-name', ...)` 监听一个事件。前端使用 `send('event-name', ...)` 来触发该事件并异步获取结果。
    *   **优点**: 类似 RPC 调用，简单直接，适合处理用户操作。

### 可扩展界面 (Slots)

Slots (插槽) 是控制台 UI 的核心扩展机制。Koishi 在界面的关键位置（如状态栏、页面全局、其他插件的特定区域）定义了插槽。您可以使用 `ctx.slot()` 方法将自己的 Vue 组件注入到任何插槽中，从而实现对界面的灵活定制。

### 配置分离

*   **插件配置**: 定义在后端，通过 `Schema` 声明，存储在 Koishi 的配置文件中。这些配置由机器人管理员设置，决定插件的整体行为。
*   **用户设置**: 定义在前端，通过 `ctx.settings()` 添加。这些配置与用户浏览器绑定，允许每个控制台用户自定义个性化选项，例如主题、布局偏好等。

## 3. 常用功能实现

### 数据交互示例

#### 被动推送 (DataService)

**后端 (`src/index.ts`)**
```ts
import { Context } from 'koishi'
import { DataService } from '@koishijs/plugin-console'

// 声明服务类型
declare module '@koishijs/plugin-console' {
  namespace Console {
    interface Services {
      stats: StatsProvider
    }
  }
}

// 实现 DataService
class StatsProvider extends DataService<object> {
  constructor(ctx: Context) {
    super(ctx, 'stats') // 'stats' 是服务的唯一标识符
  }

  async get() {
    return {
      userCount: await this.ctx.database.count('user'),
      groupCount: await this.ctx.database.count('channel'),
    }
  }
}

export const name = 'my-plugin'
export const inject = ['console', 'database']

export function apply(ctx: Context) {
  ctx.plugin(StatsProvider)

  // ... 注册前端入口 ...
}
```

**前端 (`client/index.ts` & `client/page.vue`)**
```ts title="client/index.ts"
// ...
export default (ctx: Context) => {
  ctx.page({
    name: '统计信息',
    path: '/stats',
    // 声明此页面依赖 'stats' 数据
    fields: ['stats'],
    component: () => import('./page.vue'),
  })
}
```
```vue title="client/page.vue"
<template>
  <k-layout>
    <k-card>
      <p>用户数: {{ store.stats.userCount }}</p>
      <p>群组数: {{ store.stats.groupCount }}</p>
    </k-card>
  </k-layout>
</template>

<script lang="ts" setup>
import { store } from '@koishijs/client'
</script>
```

#### 主动获取 (事件)

**后端 (`src/index.ts`)**
```ts
import { Context } from 'koishi'

// 声明事件类型
declare module '@koishijs/plugin-console' {
  interface Events {
    'my-plugin/get-time'(): number
  }
}

export const name = 'my-plugin'
export const inject = ['console']

export function apply(ctx: Context) {
  ctx.console.addListener('my-plugin/get-time', () => {
    return Date.now()
  })

  // ... 注册前端入口 ...
}
```

**前端 (`client/page.vue`)**
```vue
<template>
  <k-layout>
    <k-card>
      <p>当前服务器时间戳: {{ timestamp }}</p>
      <el-button @click="fetchTime">刷新</el-button>
    </k-card>
  </k-layout>
</template>

<script lang="ts" setup>
import { send } from '@koishijs/client'
import { ref } from 'vue'

const timestamp = ref(0)

async function fetchTime() {
  timestamp.value = await send('my-plugin/get-time')
}

fetchTime()
</script>
```

### 添加用户设置

通过 `ctx.settings()` 可以在“设置”页面的“用户设置”部分添加自定义表单。

**前端 (`client/index.ts`)**
```ts
import { Context, Schema } from '@koishijs/client'

export default (ctx: Context) => {
  ctx.settings({
    id: 'my-plugin-settings', // 唯一ID
    title: '我的插件设置',    // 分组标题
    schema: Schema.object({
      nickname: Schema.string().description('您在控制台显示的昵称。'),
      showAvatar: Schema.boolean().default(true).description('是否显示头像。'),
    }),
  })
}
```

在组件中通过 `useConfig()` 获取用户设置的值。

```vue
<script lang="ts" setup>
import { useConfig } from '@koishijs/client'

const config = useConfig()

// config.value.nickname
// config.value.showAvatar
</script>
```

## 4. 最佳实践

### 权限管理

对于敏感操作和页面，务必设置权限等级。这需要 `@koishijs/plugin-auth` 插件的支持。

**后端接口权限**
```ts
ctx.console.addListener('my-plugin/dangerous-op', () => {
  // ...
}, { authority: 4 }) // 仅限4级及以上权限的用户调用
```

**前端页面权限**
```ts
ctx.page({
  name: '高级管理',
  path: '/admin',
  authority: 4, // 仅限4级及以上权限的用户访问
  component: AdminPage,
})
```

### 善用组合式 API

`@koishijs/client` 导出了一些有用的 Vue Composition API Hooks：

*   `useContext()`: 获取当前组件所在的插件上下文 `Context`。
*   `useConfig()`: 获取响应式的用户设置对象。
*   `useMenu(id)`: 创建一个用于触发上下文菜单的事件监听器。

### 错误处理

使用 `send` 主动获取数据时，建议使用 `try...catch` 块来捕获可能发生的后端错误。

```ts
async function someAction() {
  try {
    const result = await send('some/action')
    // ...
  } catch (error) {
    // 处理错误，例如显示提示
    console.error(error)
  }
}
```

## 5. 常见问题 (FAQ)

*   **Q: 为什么我的页面没有显示？**
    A: 请检查：
    1.  插件是否已在 `koishi.config.ts` 中启用。
    2.  后端是否正确设置 `export const inject = ['console']`。
    3.  `ctx.console.addEntry()` 是否被正确调用。
    4.  浏览器控制台和 Koishi 终端是否有报错信息。

*   **Q: 为什么前端代码没有类型提示？**
    A: 请确保 `client/tsconfig.json` 文件存在且配置正确，特别是 `types` 字段中的 `@koishijs/client/global`。

*   **Q: 客户端 `Context` 和后端 `Context` 有什么区别？**
    A: 它们是两个完全不同的对象，运行在不同的环境。后端 `Context` 运行在 Node.js，拥有访问数据库、文件系统、机器人实例等权限。客户端 `Context` 运行在浏览器，主要负责注册页面、插槽、设置等前端界面元素。

## 6. 进阶技巧

### 主题开发

您可以开发自己的主题来改变控制台的外观甚至布局。

**色彩主题**
只需提供一个 CSS/SCSS 文件来覆盖 Koishi 预定义的 CSS 变量，然后使用 `ctx.theme()` 注册。

```ts
// client/index.ts
import './theme.scss'

export default (ctx: Context) => {
  ctx.theme({
    id: 'my-theme-dark', // ID 必须以 -dark 或 -light 结尾
    name: '我的暗色主题',
  })
}
```
```scss
// client/theme.scss
.theme-root[theme=my-theme-dark] {
  --bg1: #1a1a1a;
  --bg2: #2b2b2b;
  --c-text: #eaeaea;
  /* ... 更多变量 ... */
}
```

**布局主题**
通过在 `ctx.theme()` 中提供 `components` 选项，您可以完全替换控制台的核心布局组件。

```ts
import { Context } from '@koishijs/client'
import MyRoot from './MyRoot.vue'
import MyLayout from './MyLayout.vue'

export default (ctx: Context) => {
  ctx.theme({
    id: 'my-layout-theme-light',
    name: '我的布局主题',
    components: {
      root: MyRoot,     // 替换根组件
      layout: MyLayout, // 替换 <k-layout> 的实现
    },
  })
}
```

### 创建可被扩展的页面

如果希望您开发的页面能被其他插件扩展，只需在您的组件中使用 `<k-slot>` 组件。

```vue
<!-- my-plugin/client/page.vue -->
<template>
  <k-layout>
    <h2>我的仪表盘</h2>
    <div class="grid-layout">
      <!-- 定义一个名为 "dashboard-widgets" 的插槽 -->
      <k-slot name="dashboard-widgets"></k-slot>
    </div>
  </k-layout>
</template>
```

其他插件现在可以通过 `ctx.slot()` 向您的页面注入组件：

```ts
// another-plugin/client/index.ts
import { Context } from '@koishijs/client'
import MyWidget from './widget.vue'

export default (ctx: Context) => {
  ctx.slot({
    type: 'dashboard-widgets', // 目标插槽的名称
    component: MyWidget,
  })
}
```
