# YesImBot 扩展插件：代码执行器 (Code Executor)

[![npm](https://img.shields.io/npm/v/@yesimbot/koishi-plugin-code-executor.svg)](https://www.npmjs.com/package/@yesimbot/koishi-plugin-code-executor)
[![license](https://img.shields.io/npm/l/@yesimbot/koishi-plugin-code-executor.svg)](https://www.npmjs.com/package/@yesimbot/koishi-plugin-code-executor)

为 [YesImBot](https://github.com/YesWeAreBot/YesImBot) 提供一个**安全、隔离、功能强大**的 JavaScript 代码执行环境。

这个插件允许 AI 智能体编写并执行代码来完成复杂的任务，例如：

- 进行精确的数学计算和数据分析。
- 调用外部 API 获取实时信息（例如天气、新闻、股票）。
- 处理和转换文本或数据。
- 执行任何可以通过编程逻辑实现的复杂工作流。

所有代码都在一个受限的沙箱环境中运行，确保了主系统的安全。

## ✨ 主要特性

- **🔒 安全至上**: 基于 `vm2` 和 `worker_threads` 构建双重隔离沙箱，有效防止恶意代码访问文件系统、子进程或不安全的内置模块。
- **🧩 无缝集成 Yes-Im-Bot**: 作为 `yesimbot` 的扩展插件自动注册，其工具（`execute_javascript`）会直接添加到智能体的可用工具集中。
- **📦 动态依赖管理**: 智能体可以通过 `require()` 语法请求外部 npm 模块。插件会自动使用您选择的包管理器 (`npm`, `yarn`, `bun`) 安装在白名单内的依赖。
- **⚙️ 高度可配置**: 管理员可以通过白名单精确控制允许使用的 Node.js 内置模块和第三方 npm 模块。
- **⏱️ 超时与保护**: 对每一次代码执行都设置了超时限制，有效防止因死循环或长时间运行的任务而导致的资源耗尽。
- **🤖 AI 友好反馈**: 当代码执行失败时，插件会返回清晰的错误信息和**可行动的修复建议**，引导 AI 智能体自我修正代码，提高任务成功率。
- **⚡️ 结果缓存**: 可选的执行结果缓存功能，对于重复执行相同代码的场景，可以秒速返回结果，降低延迟和资源消耗。

## 💿 安装与集成

1.  **安装插件**:
    - 从 Koishi 插件市场搜索并安装 `@yesimbot/extension-code-executor`。
    - 或在您的 Koishi 项目根目录下，通过命令行安装：

        ```bash
        # 使用 npm
        npm install @yesimbot/extension-code-executor

        # 使用 yarn
        yarn add @yesimbot/extension-code-executor

        # 使用 pnpm
        pnpm add @yesimbot/extension-code-executor
        ```

2.  **自动集成**:
    - 在 Koishi 的配置文件中或通过控制台 UI 启用本插件以及 `yesimbot` 插件。
    - 本插件启动后，会自动检测并使用 `yesimbot.tool` 服务，将其自身注册为一个扩展。
    - `execute_javascript` 工具会被自动添加到所有 `yesimbot` 智能体实例的可用工具列表中，无需额外配置。

## 📝 配置项

您可以在 Koishi 控制台的插件配置页面进行设置。

| 配置项             | 类型       | 默认值                 | 描述                                                                                                         |
| :----------------- | :--------- | :--------------------- | :----------------------------------------------------------------------------------------------------------- |
| `timeout`          | `number`   | `10000`                | 代码执行的超时时间（毫秒）。超过此时间，执行将被强制终止。                                                   |
| `packageManager`   | `string`   | `"npm"`                | 用于动态安装依赖的包管理器。可选项: `"npm"`, `"yarn"`, `"bun"`。请确保您选择的管理器已在系统环境中全局安装。 |
| `allowedBuiltins`  | `string[]` | `["os", "path", ...]`  | 允许在沙箱中使用的 Node.js 内置模块白名单。                                                                  |
| `allowedModules`   | `string[]` | `[]`                   | 允许动态安装和使用的外部 npm 模块白名单。例如，可以添加 `axios` 以允许进行网络请求。                         |
| `dependenciesPath` | `string`   | `"./.sandbox_modules"` | 动态安装的 npm 模块的存放路径。                                                                              |
| `enableCache`      | `boolean`  | `true`                 | 是否启用代码执行结果缓存。                                                                                   |
| `cacheTTL`         | `number`   | `3600000`              | 缓存的有效时间（毫秒）。默认为 1 小时。                                                                      |

## 🤖 智能纠错与修复建议

这是本插件的核心优势之一，旨在提升 AI 智能体自主解决问题的能力，减少因简单错误导致的连续失败。

当 AI 提交的代码执行失败时，插件不会仅仅返回一个技术性的错误堆栈，而是会分析错误类型，并生成一段**为 AI 设计的、包含清晰指导的反馈**。

### 工作机制

1.  **错误分类**: 插件会捕捉并区分不同类型的错误，例如：
    - **安全违规**: 尝试导入未在白名单中的模块。
    - **依赖问题**: 请求的 npm 包名拼写错误或不存在，导致安装失败。
    - **执行超时**: 代码中存在死循环或效率低下的操作。
    - **运行时错误**: 代码本身存在语法错误、引用了未定义的变量等。

2.  **生成定制化建议**: 针对每种错误类型，插件会生成相应的修复建议。
    - **对于安全违规**:

        > **错误**: `模块导入失败: 模块 'fs' 不在允许的白名单中。`

        > **建议**: `建议: 你可以使用的模块列表为: [os, path, axios]。请修改代码，只使用列表中的模块，或者请求管理员将 'fs' 添加到白名单。`
        - 这不仅告知了 AI “什么错了”，还提供了“可行的替代方案”（允许的模块列表），引导其在规则范围内解决问题。

    - **对于执行超时**:

        > **错误**: `代码执行超时。最长允许执行时间为 10 秒。`

        > **建议**: `建议: 请检查你的代码是否存在无限循环或长时间运行的操作。尝试优化算法或将任务分解成更小的步骤。`
        - 这帮助 AI 理解失败并非代码逻辑错误，而是性能问题，并指明了优化的方向。

    - **对于通用运行时错误**:

        > **错误**: `代码执行时发生错误: a is not defined`

        > **建议**: `建议: 请检查代码中的语法错误、变量名拼写、以及是否正确处理了 null 或 undefined 的情况。查看下方控制台日志获取更详细的线索。`
        - 这提供了一个通用的调试清单，引导 AI 进行自我检查。

### 优势

- **提升自主性**: AI 可以根据建议自主修改代码并重试，形成一个“尝试-失败-修正-成功”的闭环，而无需人类介入。
- **降低成本**: 减少了与大语言模型之间无效的来回沟通次数，节省了 Token 消耗和时间。
- **提高任务成功率**: 显著提高了 AI 使用代码执行器完成复杂任务的最终成功率。

## 🛡️ 安全须知

**安全是本插件设计的核心，但最终的安全性取决于管理员的配置。**

- **隔离机制**: 本插件通过 `worker_threads` 将代码执行放在一个独立的线程中，并使用 `vm2` 库创建了一个安全的虚拟机（Sandbox）。这提供了**进程隔离**和**上下文隔离**，是目前 Node.js 环境下实现安全沙箱的最佳实践。
- **白名单原则**: 插件严格遵循“**默认禁止，按需授权**”的白名单原则。任何模块（无论是内置的还是第三方的）都必须由管理员在配置中明确声明后才能被 `require`。这是防止潜在安全漏洞的关键。
- **管理员责任**:
    - **请谨慎添加模块到 `allowedModules` 白名单**。一个功能强大的模块（例如，提供网络访问或系统信息访问的模块）可能会增加系统的攻击面。
    - 在添加任何新模块前，请评估其安全性。避免添加任何可能执行任意命令、访问文件系统或操纵进程的模块。
    - 定期更新 Koishi 和本插件，以获取最新的安全补丁。

## 🏗️ 工作原理

1.  **接收代码**: `index.ts` 中的 `executeJavascript` 工具接收到 AI 传来的代码字符串。
2.  **环境准备**: 主线程解析代码中的 `require()` 语句，检查所有请求的模块是否在 `allowedBuiltins` 或 `allowedModules` 白名单中。如果不在，则直接拒绝执行。
3.  **依赖安装**: 对于在白名单内但尚未安装的外部模块，主线程会调用指定的包管理器进行安装。
4.  **启动 Worker**: 主线程创建一个新的 `Worker` 线程，并将代码和配置数据（如白名单、超时时间）传递给它。
5.  **沙箱执行**: `worker.ts` 在新线程中创建一个 `NodeVM` 实例。这个 VM 根据配置，只暴露白名单中的模块，并禁用了 `fs`、`child_process` 等危险模块。
6.  **执行与监控**: 代码在 VM 中异步执行。主线程设置一个定时器来监控超时。Worker 会捕获所有的 `console` 输出。
7.  **返回结果**: Worker 执行完毕后，将捕获的 `console` 日志或发生的错误信息通过 `parentPort.postMessage` 发送回主线程。
8.  **最终反馈**: 主线程将 Worker 返回的结果包装成 `Success` 或附带修复建议的 `Failed` 对象，返回给 AI 智能体。
