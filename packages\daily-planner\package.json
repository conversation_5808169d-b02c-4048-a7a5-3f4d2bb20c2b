{"name": "koishi-plugin-yesimbot-extension-daily-planner", "description": "YesImBot 日程规划器", "version": "0.0.2", "main": "lib/index.js", "typings": "lib/index.d.ts", "files": ["lib", "README.md"], "scripts": {"build": "tsc && node esbuild.config.mjs", "dev": "tsc -w --preserveWatchOutput", "clean": "rm -rf lib .turbo tsconfig.tsbuildinfo *.tgz", "pack": "bun pm pack", "lint": "eslint . --ext .ts", "install-core": "bun add koishi-plugin-yesimbot@file:/home/<USER>/.ybe-build/*/YesImBot-dev/packages/core --dev --force"}, "license": "MIT", "keywords": ["koishi", "plugin", "extension", "yesimbot"], "repository": {"type": "git", "url": "", "directory": ""}, "devDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "^3.0.0-rc.1"}, "peerDependencies": {"koishi": "^4.18.7", "koishi-plugin-yesimbot": "^3.0.0-rc.1"}, "koishi": {"description": {"zh": "YesImBot 日程规划器", "en": "YesImBot 日程规划器"}, "service": {"required": ["yesimbot"]}}, "dependencies": {"koishi-plugin-cron": "^3.1.0"}}