import { Schema } from "koishi";
import { AgentBehaviorConfig, AgentBehaviorConfigSchema } from "./agent";
import { LoggingConfig, LoggingConfigSchema, PromptServiceConfig, PromptServiceConfigSchema } from "./services";
import { AssetServiceConfig, AssetServiceConfig as AssetServiceConfigSchema } from "./services/assets";
import { ToolServiceConfig, ToolServiceConfigSchema } from "./services/extension";
import { MemoryConfig, MemoryConfigSchema } from "./services/memory";
import { ModelServiceConfig, ModelServiceConfigSchema } from "./services/model";
import { HistoryConfig, HistoryConfigSchema } from "./services/worldstate";
import { ErrorReporterConfig, ErrorReporterConfigSchema } from "./shared/errors";

export interface SystemConfig {
    /** 全局日志配置 */
    logging: LoggingConfig;
    errorReporting: ErrorReporterConfig;
}

export const SystemConfigSchema: Schema<SystemConfig> = Schema.object({
    logging: LoggingConfigSchema,
    errorReporting: ErrorReporterConfigSchema,
});

// =================================================================
// 3. 根配置对象 (Root Configuration Object)
// =================================================================

export interface Config {
    /** AI 模型、API密钥和模型组配置 */
    modelService: ModelServiceConfig;
    /** 智能体的性格、唤醒和响应逻辑 */
    agentBehavior: AgentBehaviorConfig;
    /** 记忆、工具等扩展能力配置 */
    capabilities: {
        memory: MemoryConfig;
        /** 对话历史记录的管理方式 */
        history: HistoryConfig;
        tools: ToolServiceConfig;
    };
    /** 资源服务配置 */
    assetService: AssetServiceConfig;
    /** 提示词相关配置 */
    promptService: PromptServiceConfig;
    /** 系统缓存、调试等底层设置 */
    system: SystemConfig;
}

export const Config: Schema<Config> = Schema.object({
    modelService: ModelServiceConfigSchema.description("AI 模型、API密钥和模型组配置"),
    agentBehavior: AgentBehaviorConfigSchema,
    capabilities: Schema.object({
        memory: MemoryConfigSchema.description("记忆能力配置"),
        history: HistoryConfigSchema.description("历史记录管理"),
        tools: ToolServiceConfigSchema.description("工具能力配置"),
    }),
    assetService: AssetServiceConfigSchema.description("资源服务配置"),
    promptService: PromptServiceConfigSchema,
    system: SystemConfigSchema.description("系统设置"),
});

import { Schema } from "koishi";
import { SystemConfig } from "../../config";

/** 模型切换策略 */
export enum ModelSwitchingStrategy {
    Failover = "failover", // 故障转移 (默认)
    RoundRobin = "round-robin", // 轮询
}

/** 内容验证失败时的处理动作 */
export enum ContentFailureAction {
    FailoverToNext = "failover_to_next", // 立即切换到下一个模型
    AugmentAndRetry = "augment_and_retry", // 增强提示词并在当前模型重试
}

/** 定义超时策略 */
export interface TimeoutPolicy {
    /** 首次响应超时 (秒) */
    firstTokenTimeout?: number;
    /** 总请求超时 (秒) */
    totalTimeout: number;
}

/** 定义重试策略 */
export interface RetryPolicy {
    /** 最大重试次数 (在同一模型上) */
    maxRetries: number;
    /** 内容验证失败时的动作 */
    onContentFailure: ContentFailureAction;
}

/** 定义断路器策略 */
export interface CircuitBreakerPolicy {
    /** 触发断路的连续失败次数 */
    failureThreshold: number;
    /** 断路器开启后的冷却时间 (秒) */
    cooldownSeconds: number;
}

// =================================================================
// 1. 核心与共享类型 (Core & Shared Types)
// =================================================================

/** 定义模型支持的能力 */
export enum ModelAbility {
    Vision = "视觉",
    WebSearch = "网络搜索",
    Reasoning = "推理",
    FunctionCalling = "函数调用",
    Embedding = "嵌入",
    Chat = "对话",
}

/**
 * @enum TaskType
 * @description 定义了系统中的核心AI任务类型，用于类型安全地分配模型组。
 */
export enum TaskType {
    Chat = "chat",
    Embedding = "embed",
    Summarization = "summarize",
    Memory = "memory",
}

/** 描述一个模型在特定提供商中的位置 */
export type ModelDescriptor = {
    providerName: string;
    modelId: string;
};

// =================================================================
// 2. 配置项 - 按UI逻辑分组
// =================================================================

export interface ModelConfig {
    providerName?: string;
    modelId: string;
    abilities: ModelAbility[];
    parameters?: {
        temperature?: number;
        topP?: number;
        stream?: boolean;
        custom?: Array<{ key: string; type: "string" | "number" | "boolean" | "object"; value: string }>;
    };
    /** 超时策略 */
    timeoutPolicy?: TimeoutPolicy;
    /** 重试策略 */
    retryPolicy?: RetryPolicy;
    /** 断路器策略 */
    circuitBreakerPolicy?: CircuitBreakerPolicy;
}

export const ModelConfigSchema: Schema<ModelConfig> = Schema.object({
    modelId: Schema.string().required().description("模型ID"),
    abilities: Schema.array(
        Schema.union([
            ModelAbility.Chat,
            ModelAbility.Vision,
            ModelAbility.WebSearch,
            ModelAbility.Reasoning,
            ModelAbility.FunctionCalling,
            ModelAbility.Embedding,
        ])
    )
        .role("checkbox")
        .default([ModelAbility.Chat, ModelAbility.FunctionCalling])
        .description("模型支持的能力"),

    parameters: Schema.object({
        temperature: Schema.number().default(0.85),
        topP: Schema.number().default(0.95),
        stream: Schema.boolean().default(true).description("流式传输"),
        custom: Schema.array(
            Schema.object({
                key: Schema.string().required(),
                type: Schema.union(["string", "number", "boolean", "object"]).default("string"),
                value: Schema.string().required(),
            })
        )
            .role("table")
            .description("自定义参数"),
    }),

    timeoutPolicy: Schema.object({
        firstTokenTimeout: Schema.number().default(15).description("首字响应超时 (秒)"),
        totalTimeout: Schema.number().default(60).description("总请求超时 (秒)"),
    }).description("超时策略"),

    retryPolicy: Schema.object({
        maxRetries: Schema.number().default(1).description("在切换到下一个模型前，在当前模型上的最大重试次数"),
        onContentFailure: Schema.union([
            Schema.const(ContentFailureAction.FailoverToNext).description("立即切换"),
            Schema.const(ContentFailureAction.AugmentAndRetry).description("修正Prompt并重试"),
        ])
            .default(ContentFailureAction.AugmentAndRetry)
            .description("响应内容无效时的处理方式"),
    }).description("重试策略"),

    circuitBreakerPolicy: Schema.object({
        failureThreshold: Schema.number().default(3).description("连续失败多少次后开启断路器"),
        cooldownSeconds: Schema.number().default(300).description("断路器开启后，模型被禁用的时长(秒)"),
    }).description("断路器策略"),
})
    .collapse()
    .description("单个模型配置");

const PROVIDERS = {
    OpenAI: { baseURL: "https://api.openai.com/v1/", link: "https://platform.openai.com/account/api-keys" },
    "OpenAI Compatible": { baseURL: "https://api.openai.com/v1/", link: "https://platform.openai.com/account/api-keys" },
    Anthropic: { baseURL: "https://api.anthropic.com/v1/", link: "https://console.anthropic.com/settings/keys" },
    Fireworks: { baseURL: "https://api.fireworks.ai/inference/v1/", link: "https://console.fireworks.ai/api-keys" },
    DeepSeek: { baseURL: "https://api.deepseek.com/", link: "https://platform.deepseek.com/api_keys" },
    "Google Gemini": {
        baseURL: "https://generativelanguage.googleapis.com/v1beta/openai/",
        link: "https://aistudio.google.com/app/apikey",
    },
    "LM Studio": { baseURL: "http://localhost:5000/v1/", link: "https://lmstudio.ai/docs/app/api/endpoints/openai" },
    "Workers AI": { baseURL: "https://api.cloudflare.com/client/v4/", link: "https://dash.cloudflare.com/?to=/:account/workers-ai" },
    Zhipu: { baseURL: "https://open.bigmodel.cn/api/paas/v4/", link: "https://open.bigmodel.cn/usercenter/apikeys" },
    "Silicon Flow": { baseURL: "https://api.siliconflow.cn/v1/", link: "https://console.siliconflow.cn/account/key" },
    Qwen: { baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1/", link: "https://dashscope.console.aliyun.com/apiKey" },
    Ollama: { baseURL: "http://localhost:11434/v1/", link: "https://ollama.com/" },
    // "Azure OpenAI": {
    //     baseURL: "https://<resource-name>.services.ai.azure.com/models/",
    //     link: "https://oai.azure.com/",
    // },
    Cerebras: { baseURL: "https://api.cerebras.ai/v1/", link: "https://inference-docs.cerebras.ai/api-reference/chat-completions" },
    DeepInfra: { baseURL: "https://api.deepinfra.com/v1/openai/", link: "https://deepinfra.com/dash/api_keys" },
    "Fatherless AI": { baseURL: "https://api.featherless.ai/v1/", link: "https://featherless.ai/login" },
    Groq: { baseURL: "https://api.groq.com/openai/v1/", link: "https://console.groq.com/keys" },
    Minimax: { baseURL: "https://api.minimax.chat/v1/", link: "https://platform.minimaxi.com/api-key" },
    "Minimax (International)": { baseURL: "https://api.minimaxi.chat/v1/", link: "https://www.minimax.io/user-center/api-keys" },
    Mistral: { baseURL: "https://api.mistral.ai/v1/", link: "https://console.mistral.ai/api-keys/" },
    Moonshot: { baseURL: "https://api.moonshot.cn/v1/", link: "https://platform.moonshot.cn/console/api-keys" },
    Novita: { baseURL: "https://api.novita.ai/v3/openai/", link: "https://novita.ai/get-started" },
    OpenRouter: { baseURL: "https://openrouter.ai/api/v1/", link: "https://openrouter.ai/keys" },
    Perplexity: { baseURL: "https://api.perplexity.ai/", link: "https://www.perplexity.ai/settings/api" },
    Stepfun: { baseURL: "https://api.stepfun.com/v1/", link: "https://platform.stepfun.com/my-keys" },
    "Tencent Hunyuan": { baseURL: "https://api.hunyuan.cloud.tencent.com/v1/", link: "https://console.cloud.tencent.com/cam/capi" },
    "Together AI": { baseURL: "https://api.together.xyz/v1/", link: "https://api.together.ai/settings/api-keys" },
    "XAI (Grok)": { baseURL: "https://api.x.ai/v1/", link: "https://docs.x.ai/docs/overview" },
} as const;

export const PROVIDER_TYPES = Object.keys(PROVIDERS) as ProviderType[];

export type ProviderType = keyof typeof PROVIDERS;

export interface ProviderConfig {
    name: string;
    type: ProviderType;
    baseURL?: string;
    apiKey: string;
    proxy?: string;
    models: ModelConfig[];
}

export const ProviderConfigSchema: Schema<ProviderConfig> = Schema.intersect([
    Schema.object({
        name: Schema.string().required().description("提供商名称"),
        type: Schema.union(PROVIDER_TYPES).default("OpenAI").description("提供商类型"),
    }),
    Schema.union(
        PROVIDER_TYPES.map((type) => {
            return Schema.object({
                type: Schema.const(type),
                baseURL: Schema.string().default(PROVIDERS[type].baseURL).role("link").description(`提供商的 API 地址`),
                apiKey: Schema.string()
                    .role("secret")
                    .description(`提供商的 API 密钥${PROVIDERS[type].link ? ` (获取地址 - ${PROVIDERS[type].link})` : ""}`),
                proxy: Schema.string().description("代理地址"),
                models: Schema.array(ModelConfigSchema).required().description("模型列表"),
            });
        })
    ),
])
    .collapse()
    .description("提供商配置");

export interface ModelServiceConfig {
    providers: ProviderConfig[];
    modelGroups: { name: string; models: ModelDescriptor[]; strategy: ModelSwitchingStrategy }[];
    task: {
        [TaskType.Chat]: string;
        [TaskType.Embedding]: string;
    };
    readonly system?: SystemConfig;
}

export const ModelServiceConfigSchema: Schema<ModelServiceConfig> = Schema.object({
    providers: Schema.array(ProviderConfigSchema).required().role("table").description("配置你的 AI 模型提供商，如 OpenAI, Anthropic 等"),
    modelGroups: Schema.array(
        Schema.object({
            name: Schema.string().required().description("模型组名称"),
            strategy: Schema.union([
                Schema.const(ModelSwitchingStrategy.Failover).description("故障转移"),
                Schema.const(ModelSwitchingStrategy.RoundRobin).description("轮询/负载均衡"),
            ])
                .default(ModelSwitchingStrategy.Failover)
                .description("模型切换策略"),
            models: Schema.array(Schema.dynamic("modelService.selectableModels"))
                .required()
                .role("table")
                .description("此模型组包含的模型"),
        }).collapse()
    )
        .role("table")
        .description("**［必填］** 创建**模型组**，用于故障转移或分类。每次修改模型配置后，需要先启动/重载一次插件来修改此处的值"),
    task: Schema.object({
        [TaskType.Chat]: Schema.dynamic("modelService.availableGroups").description(
            "主要聊天功能使用的模型**组**<br/>如 `gpt-4` `claude-3` `gemini-2.5` 等对话模型"
        ),
        [TaskType.Embedding]: Schema.dynamic("modelService.availableGroups").description(
            "生成文本嵌入(Embedding)时使用的模型**组**<br/>如 `bge-m3` `text-embedding-3-small` 等嵌入模型"
        ),
    }).description("为不同核心任务分配一个模型组"),
});

import { readFileSync } from "fs";
import { Computed, Schema } from "koishi";
import path from "path";

import { SystemConfig } from "@/config";
import { PROMPTS_DIR } from "@/shared/constants";

// 默认的系统和用户模板文件路径
export const SystemBaseTemplate = readFileSync(path.resolve(PROMPTS_DIR, "memgpt_v2_chat.txt"), "utf-8");
export const UserBaseTemplate = readFileSync(path.resolve(PROMPTS_DIR, "user_base.txt"), "utf-8");
export const MultiModalSystemBaseTemplate = `Images that appear in the conversation will be provided first, numbered in the format 'Image #[ID]:'.
In the subsequent conversation text, placeholders in the format <img id="[ID]" /> will be used to refer to these images.
Please participate in the conversation considering the full context of both images and text.
If image data is not provided, use \`get_image_description\` to describe the image.`;

export type ChannelDescriptor = {
    platform: string;
    type: "private" | "guild";
    id: string;
};

/** Agent 的唤醒条件配置 */
export interface ArousalConfig {
    /**
     * 允许 Agent 响应的频道。
     * 这是一个 "OR" 关系的列表。
     */
    allowedChannels: ChannelDescriptor[];
    /** 消息防抖时间 (毫秒)，防止短时间内对相同模式的重复响应 */
    debounceMs: number;
}

export const ArousalConfigSchema: Schema<ArousalConfig> = Schema.object({
    allowedChannels: Schema.array(
        Schema.object({
            platform: Schema.string().required().description("平台"),
            type: Schema.union([Schema.const("private").description("私聊"), Schema.const("guild").description("群组")])
                .default("guild")
                .description("频道类型"),
            id: Schema.string().required().description("频道 ID"),
        })
    )
        .role("table")
        .default([{ platform: "onebot", type: "guild", id: "*" }])
        .description("允许 Agent 响应的频道。使用 * 作为通配符"),
    debounceMs: Schema.number().default(1000).description("消息防抖时间 (毫秒)"),
});

/** Agent 的响应意愿配置 (决定是否响应) */
export interface WillingnessConfig {
    // --- A. 基础分数 (Base Scores) ---
    // 定义不同消息类型的"基础反应分"。它们通常是互斥的。

    base: {
        /** 收到普通文本消息的基础分。这是对话的基石 */
        text: Computed<number>;
        /** 收到图片消息的基础分。可以设为负数让AI不爱理睬图片 */
        //image: Computed<number>;
        /** 收到表情/贴纸的基础分。通常较低 */
        //emoji: Computed<number>;
    };

    // --- B. 属性加成 (Attribute Bonuses) ---
    // 如果消息满足以下属性，在基础分之上额外增加的分数。可以叠加。
    attribute: {
        /** 被 @ 提及时的额外加成。这是最高优先级的信号 */
        atMention: Computed<number>;
        /** 作为"回复/引用"出现时的额外加成。表示对话正在延续 */
        isQuote: Computed<number>;
        /** 在私聊场景下的额外加成。私聊通常期望更高的响应度 */
        isDirectMessage: Computed<number>;
    };

    // --- C. 兴趣度模型 (Interest Model) ---
    // 基于内容计算一个乘数，影响最终得分。
    interest: {
        /** 触发"高兴趣"的关键词列表 */
        keywords: Computed<string[]>;
        /** 消息包含关键词时，应用此乘数。>1 表示增强，<1 表示削弱 */
        keywordMultiplier: Computed<number>;
        /** 默认乘数（当没有关键词匹配时）。设为1表示不影响 */
        defaultMultiplier: Computed<number>;
    };

    // --- D. 意愿转换与生命周期 (Lifecycle & Conversion) ---
    lifecycle: {
        /** 意愿值的最大上限 */
        maxWillingness: Computed<number>;
        /** 意愿值衰减到一半所需的时间（秒）。这是一个基础值，会受对话热度影响 */
        decayHalfLifeSeconds: Computed<number>;
        /** 将意愿值转换为回复概率的"激活门槛" */
        probabilityThreshold: Computed<number>;
        /** 超过门槛后，转换为概率时的放大系数 */
        probabilityAmplifier: Computed<number>;
        /** 决定回复后，扣除的"发言精力惩罚"基础值 */
        replyCost: Computed<number>;
        /**
         * 回复后的一段“不应期”（毫秒），在此期间不会再次响应。
         *  这可以有效防止 AI 连续回复，显得更自然。
         */
        //refractoryPeriodMs: Computed<number>;
    };

    readonly system?: SystemConfig;
}

const WillingnessConfigSchema: Schema<WillingnessConfig> = Schema.object({
    base: Schema.object({
        text: Schema.computed<Schema<number>>(Schema.number().default(10)).default(10).description("收到普通文本消息的基础分"),
        //image: Schema.computed<Schema<number>>(Schema.number()).default(2).description("收到图片消息的基础分"),
        //emoji: Schema.computed<Schema<number>>(Schema.number()).default(1).description("收到表情的基础分"),
    }),
    attribute: Schema.object({
        atMention: Schema.computed<Schema<number>>(Schema.number().default(100)).default(100).description("被@时的额外加成"),
        isQuote: Schema.computed<Schema<number>>(Schema.number().default(15)).default(15).description("作为回复/引用时的额外加成"),
        isDirectMessage: Schema.computed<Schema<number>>(Schema.number().default(40)).default(40).description("在私聊场景下的额外加成"),
    }),
    interest: Schema.object({
        keywords: Schema.computed<Schema<string[]>>(Schema.array(Schema.string()).default([]))
            .role("table")
            .default([])
            .description("触发高兴趣的关键词"),
        keywordMultiplier: Schema.computed<Schema<number>>(Schema.number().default(1.2)).default(1.2).description("包含关键词时的乘数"),
        defaultMultiplier: Schema.computed<Schema<number>>(Schema.number().default(1)).default(1).description("默认乘数"),
    }),
    lifecycle: Schema.object({
        maxWillingness: Schema.computed<Schema<number>>(Schema.number().default(100)).min(10).default(100).description("意愿值的最大上限"),
        decayHalfLifeSeconds: Schema.computed<Schema<number>>(Schema.number().default(90))
            .min(5)
            .default(90)
            .description("意愿值衰减到一半所需的时间（秒）"),
        probabilityThreshold: Schema.computed<Schema<number>>(Schema.number().default(60))
            .min(0)
            .default(60)
            .description("将意愿值转换为回复概率的激活门槛"),
        probabilityAmplifier: Schema.computed<Schema<number>>(Schema.number().default(0.05))
            .min(0.01)
            .max(1)
            .default(0.05)
            .description("概率放大系数"),
        replyCost: Schema.computed<Schema<number>>(Schema.number().default(30))
            .min(0)
            .default(30)
            .description('决定回复后，扣除的"发言精力惩罚"'),
        // refractoryPeriodMs: Schema.computed<Schema<number>>(Schema.number())
        //     .min(0)
        //     .default(3000)
        //     .description("回复后的“不应期”（毫秒），防止AI连续发言"),
    }),
});

/** 视觉与多模态相关配置 */
export interface VisionConfig {
    /** 是否启用视觉功能 */
    enabled: boolean;
    /** 允许的图片类型 */
    allowedImageTypes: string[];
    /** 允许在上下文中包含的最大图片数量 */
    maxImagesInContext: number;
    /**
     * 图片在上下文中的最大生命周期。
     * 一张图片在上下文中出现 N 次后将被视为"过期"，除非它被引用。
     */
    imageLifecycleCount: number;
    detail: "low" | "high" | "auto";
}

export const VisionConfigSchema: Schema<VisionConfig> = Schema.object({
    enabled: Schema.boolean().default(false).description("是否启用视觉功能"),
    allowedImageTypes: Schema.array(Schema.string()).default(["image/jpeg", "image/png"]).description("允许的图片类型"),
    maxImagesInContext: Schema.number().default(3).description("在上下文中允许包含的最大图片数量"),
    imageLifecycleCount: Schema.number().default(2).description("图片的上下文生命周期（出现次数）。超过此次数的图片将被忽略，除非被引用"),
    detail: Schema.union(["low", "high", "auto"]).default("low").description("图片细节程度"),
});

/**
 * 智能体行为总体配置
 */
export interface AgentBehaviorConfig {
    arousal: ArousalConfig;
    willingness: WillingnessConfig;
    streamAction: boolean;
    heartbeat: number;
    prompt: {
        systemTemplate: string;
        userTemplate: string;
        multiModalSystemTemplate: string;
    };
    vision: VisionConfig;
    readonly system?: SystemConfig;

    /**
     * 当处理消息过程中收到新消息时的处理策略
     * - skip: 跳过此消息（默认行为）
     * - immediate: 处理完当前消息后立即处理新消息
     * - deferred: 等待安静期后处理被跳过的话题
     */
    newMessageStrategy: "skip" | "immediate" | "deferred";

    /**
     * 延迟处理策略的安静期时间（毫秒）
     * 当一段时间内没有新消息时才处理被跳过的话题
     */
    deferredProcessingTime?: number;
}

export const AgentBehaviorConfigSchema: Schema<AgentBehaviorConfig> = Schema.object({
    arousal: ArousalConfigSchema.description("唤醒条件"),
    willingness: WillingnessConfigSchema.description("响应意愿"),
    streamAction: Schema.boolean().default(false).experimental(),
    heartbeat: Schema.number().min(1).max(10).default(5).role("slider").step(1).description("每轮对话最大心跳次数"),
    prompt: Schema.object({
        systemTemplate: Schema.string()
            .default(SystemBaseTemplate)
            .role("textarea", { rows: [2, 4] })
            .description("系统提示词模板"),
        userTemplate: Schema.string()
            .default(UserBaseTemplate)
            .role("textarea", { rows: [2, 4] })
            .description("用户提示词模板"),
        multiModalSystemTemplate: Schema.string()
            .default(MultiModalSystemBaseTemplate)
            .role("textarea", { rows: [2, 4] })
            .description("多模态系统提示词 (用于向模型解释图片占位符)"),
    }).description("提示词模板"),
    vision: VisionConfigSchema.description("视觉与多模态配置"),
    newMessageStrategy: Schema.union([
        Schema.const("skip").description("跳过新消息（默认）"),
        Schema.const("immediate").description("立即处理新消息"),
        Schema.const("deferred").description("延迟处理被跳过话题"),
    ])
        .default("skip")
        .description("处理新消息的策略"),
    deferredProcessingTime: Schema.number()
        .default(10000) // 默认10秒
        .description("延迟处理策略的安静期时间（毫秒）"),
});

import { Schema } from "koishi";

/** 记忆服务配置 */
export interface MemoryConfig {
    coreMemoryPath: string;
}

export const MemoryConfigSchema: Schema<MemoryConfig> = Schema.object({
    coreMemoryPath: Schema.path({ allowCreate: true, filters: ["directory"] })
        .default("data/yesimbot/memory/core")
        .description("核心记忆文件的存放路径"),
});

import { Schema } from "koishi";

import { ChannelDescriptor } from "@/agent";
import { SystemConfig } from "@/config";

/**
 * 多级缓存记忆模型管理配置
 */
export interface HistoryConfig {
    /* === L1 工作记忆 === */
    l1_memory: {
        /** 工作记忆中最多包含的消息数量，超出部分将被平滑裁剪 */
        maxMessages: number;
        /** pending 状态的轮次在多长时间内没有新消息后被强制关闭（秒） */
        pendingTurnTimeoutSec: number;
        /** 保留完整 Agent 响应（思考、行动、观察）的最新轮次数 */
        keepFullTurnCount: number;
    };

    /* === L2 语义索引 === */
    l2_memory: {
        /** 启用 L2 记忆检索 */
        enabled: boolean;
        /** 检索时返回的最大记忆片段数量 */
        retrievalK: number;
        /** 向量相似度搜索的最低置信度阈值，低于此值的结果将被过滤 */
        retrievalMinSimilarity: number;
        /** 每个语义记忆片段包含的消息数量 */
        messagesPerChunk: number;
        /** 是否扩展相邻chunk */
        includeNeighborChunks: boolean;
    };

    /* === L3 长期存档 === */
    l3_memory: {
        /** 启用 L3 日记功能 */
        enabled: boolean;
        /** 每日生成日记的时间 (HH:mm) */
        diaryGenerationTime: string;
    };
    ignoreSelfMessage: boolean;

    /* === 清理 === */
    dataRetentionDays: number;
    cleanupIntervalSec: number;

    readonly allowedChannels?: ChannelDescriptor[];
    readonly system?: SystemConfig;
}

export const HistoryConfigSchema: Schema<HistoryConfig> = Schema.object({
    l1_memory: Schema.object({
        maxMessages: Schema.number().default(50).description("L1工作记忆中最多包含的消息数量，超出部分将被平滑裁剪"),
        pendingTurnTimeoutSec: Schema.number().default(1800).description("等待处理的交互轮次在多长时间无新消息后被强制关闭（秒）"),
        keepFullTurnCount: Schema.number().default(2).description("保留完整 Agent 响应（思考、行动、观察）的最新轮次数"),
    }),

    l2_memory: Schema.object({
        enabled: Schema.boolean().default(true).description("启用 L2 语义记忆检索功能 (RAG)"),
        retrievalK: Schema.number().default(8).description("每次从 L2 检索的最大记忆片段数量"),
        retrievalMinSimilarity: Schema.number().default(0.55).description("向量相似度搜索的最低置信度阈值，低于此值的结果将被过滤"),
        messagesPerChunk: Schema.number().default(4).description("每个语义记忆片段包含的消息数量"),
        includeNeighborChunks: Schema.boolean().default(true).description("是否扩展前后相邻的记忆片段"),
    }).description("语义索引设置"),

    l3_memory: Schema.object({
        enabled: Schema.boolean().default(false).description("启用 L3 长期日记功能"),
        diaryGenerationTime: Schema.string().default("04:00").description("每日生成日记的时间（HH:mm 格式）"),
    })
        .hidden()
        .description("长期存档设置"),

    ignoreSelfMessage: Schema.boolean().default(false).description("是否忽略自身发送的消息"),
    dataRetentionDays: Schema.number().default(30).description("历史数据在被永久删除前的最大保留天数"),
    cleanupIntervalSec: Schema.number().default(300).description("后台清理任务的执行频率（秒）"),
});

import { SystemConfig } from "@/config";
import { Schema } from "koishi";

export interface ToolServiceConfig {
    extra?: Record<string, { enabled?: boolean; [key: string]: any }>;
    /** 高级选项 */
    advanced?: {
        maxRetry?: number;
        retryDelay?: number;
        timeout?: number;
    };
    readonly system?: SystemConfig;
}

export const ToolServiceConfigSchema = Schema.object({
    extra: Schema.dynamic("toolService.availableExtensions").default({}),

    advanced: Schema.object({
        maxRetry: Schema.number().default(3).description("最大重试次数"),
        retryDelay: Schema.number().default(1000).description("重试延迟时间（毫秒）"),
        timeout: Schema.number().default(10000).description("超时时间（毫秒）"),
    })
        .collapse()
        .description("高级选项"),
});

import { Schema } from "koishi";

export interface AssetServiceConfig {
    storagePath: string;
    driver: "local";
    endpoint?: string;
    maxFileSize: number;
    downloadTimeout: number;
    autoClear: {
        enabled: boolean;
        intervalHours: number;
        maxAgeDays: number;
    };
    image: {
        processedCachePath: string;
        //resizeEnabled: boolean;
        targetSize: number;
        maxSizeMB: number;
        gifProcessingStrategy: "firstFrame" | "stitch";
        gifFramesToExtract: number;
    };
    recoveryEnabled: boolean;
}

export const AssetServiceConfig: Schema<AssetServiceConfig> = Schema.object({
    storagePath: Schema.path({ allowCreate: true, filters: ["directory"] })
        .default("data/assets")
        .description("资源本地存储路径"),

    driver: Schema.union(["local"]).default("local").description("存储驱动类型"),

    endpoint: Schema.string().role("link").description("公开访问端点 URL (可选)"),

    maxFileSize: Schema.number().min(1).default(100).description("允许存储的单个文件的最大大小（MB）"),
    downloadTimeout: Schema.number().min(1000).default(30000).description("下载外部资源的超时时间（毫秒）"),

    autoClear: Schema.object({
        enabled: Schema.boolean().default(true).description("是否启用自动清理过期资源"),
        intervalHours: Schema.number().min(1).default(24).description("自动清理周期（小时）"),
        maxAgeDays: Schema.number().min(1).default(7).description("资源最长保留天数"),
    }).description("自动清理配置"),

    image: Schema.object({
        processedCachePath: Schema.path({ allowCreate: true, filters: ["directory"] })
            .default("data/assets/processed")
            .description("处理后图片的缓存存储路径"),
        //resizeEnabled: Schema.boolean().default(true).description("读取图片时是否启用动态缩放和压缩"),
        targetSize: Schema.union([512, 768, 1024, 1536, 2048]).default(1024).description("图片处理后长边的目标最大像素") as Schema<number>,
        maxSizeMB: Schema.number().min(0.5).max(10).default(3).description("处理后图片文件的最大体积（MB）"),
        gifProcessingStrategy: Schema.union(["firstFrame", "stitch"])
            .default("stitch")
            .description("GIF 动图处理策略：'firstFrame' (提取第一帧) 或 'stitch' (拼接多帧)"),
        gifFramesToExtract: Schema.number().min(2).max(16).default(6).description("当策略为 'stitch' 时，提取并拼接的 GIF 关键帧数量"),
    }).description("图片处理配置"),

    recoveryEnabled: Schema.boolean().default(true).description("是否启用资源恢复机制"),
});
import { Schema } from "koishi";

/**
 * PromptService 配置接口
 */
export interface PromptServiceConfig {
    /**
     * 在模板中用于注入所有扩展片段的占位符名称。
     * @default 'extensions'
     */
    injectionPlaceholder?: string;
    /**
     * 模板渲染的最大深度，用于支持片段的二次渲染，同时防止无限循环。
     * @default 3
     */
    maxRenderDepth?: number;
}

export const PromptServiceConfigSchema: Schema<PromptServiceConfig> = Schema.object({
    injectionPlaceholder: Schema.string().default("extensions").description("用于注入所有扩展片段的占位符名称。"),
    maxRenderDepth: Schema.number().default(3).min(1).description("模板渲染的最大深度，用于支持二次渲染并防止无限循环。"),
});
