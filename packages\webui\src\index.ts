import { Context, Schema } from "koishi";
import { resolve } from "path";
import {} from "@koishijs/console";

import YesImBot from "koishi-plugin-yesimbot";

export const inject = ["console", "yesimbot"];

interface Config {}

export const Config: Schema<Config> = Schema.object({});

const YesImBotConfig = YesImBot.Config;

export function apply(ctx: Context) {
    // 注册前端页面资源
    ctx.console.addEntry({
        // 开发环境入口
        dev: resolve(__dirname, "../client/index.ts"),
        // 生产环境入口 (需要构建)
        prod: resolve(__dirname, "../dist"),
    });
}
