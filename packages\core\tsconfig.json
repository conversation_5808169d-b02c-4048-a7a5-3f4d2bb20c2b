{"extends": "../../tsconfig.base", "compilerOptions": {"target": "es2022", "module": "CommonJS", "moduleResolution": "node", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "emitDeclarationOnly": false, "rootDir": "src", "outDir": "lib", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src"], "exclude": ["node_modules", "dist", "lib"]}